/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import {
  Box,
  Group,
  Title,
  Text,
  Stack,
  Center,
  Button,
  Container,
  Flex,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';

// Icon components
function VisaIcon({ isSmall }: { isSmall: boolean }) {
  const size = isSmall ? { width: '36', height: '24' } : { width: '48', height: '32' };
  return (
    <svg {...size} viewBox="0 0 48 32" fill="none">
      <rect width="48" height="32" rx="4" fill="#1A1F71" />
      <text x="24" y="20" textAnchor="middle" fill="white" fontSize="12" fontWeight="bold">VISA</text>
    </svg>
  );
}

function MastercardIcon({ isSmall }: { isSmall: boolean }) {
  const size = isSmall ? { width: '36', height: '24' } : { width: '48', height: '32' };
  return (
    <svg {...size} viewBox="0 0 48 32" fill="none">
      <rect width="48" height="32" rx="4" fill="white" />
      <circle cx="18" cy="16" r="8" fill="#FF5F00" />
      <circle cx="30" cy="16" r="8" fill="#EB001B" />
      <circle cx="24" cy="16" r="8" fill="#F79E1B" fillOpacity="0.8" />
    </svg>
  );
}

function CreditCardIcon({ size }: { size: number }) {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="3" y="7" width="18" height="10" rx="2" ry="2" fill="#6B7280" />
      <rect x="3" y="9" width="18" height="2" fill="#374151" />
      <rect x="5" y="13" width="3" height="1" rx="0.5" fill="#9CA3AF" />
      <rect x="10" y="13" width="5" height="1" rx="0.5" fill="#9CA3AF" />
    </svg>
  );
}

function CashIcon({ size }: { size: number }) {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="3" y="7" width="18" height="10" rx="2" ry="2" fill="#6B7280" />
      <text x="12" y="13.5" textAnchor="middle" fill="#FFFFFF" fontSize="8" fontWeight="bold">$</text>
      <circle cx="7" cy="12" r="1" fill="#9CA3AF" />
      <circle cx="17" cy="12" r="1" fill="#9CA3AF" />
    </svg>
  );
}

// Feature item component
function FeatureItem({
  icon,
  text,
  isMobile,
}: {
  icon: React.ReactNode;
  text: string;
  isMobile: boolean;
}) {
  return (
    <Box
      sx={() => ({
        backgroundColor: '#F3F4F6',
        borderRadius: 16,
        padding: isMobile ? '20px' : '24px',
        textAlign: 'center',
        border: '1px solid #E5E7EB',
        transition: 'all 0.2s ease',
        minWidth: isMobile ? 100 : 120,
        minHeight: isMobile ? 100 : 120,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '&:hover': {
          backgroundColor: '#E5E7EB',
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        },
      })}
    >
      <Stack align="center" spacing={isMobile ? 'sm' : 'md'}>
        <Center
          sx={() => ({
            width: isMobile ? 48 : 56,
            height: isMobile ? 48 : 56,
            borderRadius: '50%',
            backgroundColor: '#FFFFFF',
            border: '2px solid #E5E7EB',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
            flexShrink: 0,
          })}
        >
          {icon}
        </Center>
        <Text
          weight={600}
          size={isMobile ? 'sm' : 'md'}
          color="#374151"
          align="center"
          sx={{
            lineHeight: 1.3,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {text}
        </Text>
      </Stack>
    </Box>
  );
}

// Mobile layout component
function MobileLayout({
  t,
  isSmallMobile,
  iconSize,
}: {
  t: (key: string) => string;
  isSmallMobile: boolean;
  iconSize: number;
}) {
  return (
    <Stack spacing="xl">
      <Box>
        <Group spacing={isSmallMobile ? 'xs' : 'sm'} mb="sm" position="center">
          <VisaIcon isSmall={isSmallMobile} />
          <MastercardIcon isSmall={isSmallMobile} />
        </Group>
        <Title
          order={2}
          align="center"
          size={isSmallMobile ? 'h3' : 'h2'}
          mb="xs"
        >
          {t('common:flexiCardTitle')}
        </Title>
        <Text
          align="center"
          size="sm"
          color="dimmed"
          px={isSmallMobile ? 0 : 'md'}
        >
          {t('common:flexiCardDescription')}
        </Text>
      </Box>
      <Group
        position="center"
        spacing={isSmallMobile ? 'md' : 'xl'}
        sx={{
          flexWrap: isSmallMobile ? 'wrap' : 'nowrap',
          justifyContent: 'center',
        }}
      >
        <FeatureItem icon={<CreditCardIcon size={iconSize} />} text="Pay anywhere" isMobile />
        <FeatureItem icon={<CashIcon size={iconSize} />} text="Cashback" isMobile />
      </Group>
    </Stack>
  );
}

// Desktop layout component
function DesktopLayout({
  t,
  isTablet,
  isSmallMobile,
  iconSize,
}: {
  t: (key: string) => string;
  isTablet: boolean;
  isSmallMobile: boolean;
  iconSize: number;
}) {
  return (
    <Flex
      justify="space-between"
      align={isTablet ? 'flex-start' : 'center'}
      direction={isTablet ? 'column' : 'row'}
      gap={isTablet ? 'xl' : 'lg'}
    >
      <Box sx={{ flex: isTablet ? 'none' : 1 }}>
        <Group spacing="sm" mb="sm" position={isTablet ? 'center' : 'left'}>
          <VisaIcon isSmall={isSmallMobile} />
          <MastercardIcon isSmall={isSmallMobile} />
        </Group>
        <Title
          order={1}
          mb="xs"
          align={isTablet ? 'center' : 'left'}
          size={isTablet ? 'h2' : 'h1'}
        >
          {t('common:flexiCardTitle')}
        </Title>
        <Text
          size="md"
          color="dimmed"
          align={isTablet ? 'center' : 'left'}
          maw={isTablet ? 'none' : 400}
        >
          {t('common:flexiCardDescription')}
        </Text>
      </Box>
      <Group
        spacing={isTablet ? 'xl' : 60}
        position={isTablet ? 'center' : 'right'}
        sx={{ flex: 'none' }}
      >
        <FeatureItem icon={<CreditCardIcon size={iconSize} />} text="Pay anywhere" isMobile={false} />
        <FeatureItem icon={<CashIcon size={iconSize} />} text="Cashback" isMobile={false} />
      </Group>
    </Flex>
  );
}

export default function ApplyBanner() {
  const { t } = useTranslation();

  // Responsive breakpoints
  const isMobile = useMediaQuery('(max-width: 48em)');
  const isTablet = useMediaQuery('(max-width: 62em)');
  const isSmallMobile = useMediaQuery('(max-width: 30em)');

  const iconSize = isMobile ? 20 : 24;

  return (
    <Container
      size="xl"
      py={isMobile ? 'md' : 'xl'}
      px={{
        xs: 16,
        sm: 20,
        md: 24,
        lg: 32,
      }}
    >
      <Flex justify="flex-end" mb={isMobile ? 'md' : 'lg'}>
        <Button
          variant="subtle"
          color="gray"
          size={isMobile ? 'xs' : 'sm'}
          compact={isMobile}
        >
          {t('common:redeemCard')}
          {' '}
          →
        </Button>
      </Flex>

      {isMobile ? (
        <MobileLayout
          t={t}
          isSmallMobile={isSmallMobile}
          iconSize={iconSize}
        />
      ) : (
        <DesktopLayout
          t={t}
          isTablet={isTablet}
          isSmallMobile={isSmallMobile}
          iconSize={iconSize}
        />
      )}
    </Container>
  );
}
